'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'

export interface SearchBarProps {
  onSearch?: (query: string, location: string) => void
  placeholder?: string
  className?: string
}

export function SearchBar({ 
  onSearch, 
  placeholder = "Search farms, activities, or species...",
  className 
}: SearchBarProps) {
  const [query, setQuery] = useState('')
  const [location, setLocation] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch?.(query, location)
  }

  return (
    <form onSubmit={handleSubmit} className={className}>
      <div className="flex flex-col md:flex-row gap-4 p-6 bg-white rounded-lg shadow-lg">
        <div className="flex-1">
          <Input
            type="text"
            placeholder={placeholder}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="border-earth-300 focus:border-accent-600"
          />
        </div>
        
        <div className="flex-1 md:max-w-xs">
          <Input
            type="text"
            placeholder="Location (province, city)"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            className="border-earth-300 focus:border-accent-600"
          />
        </div>
        
        <Button 
          type="submit" 
          variant="primary" 
          size="lg"
          className="md:px-8"
        >
          🔍 Search
        </Button>
      </div>
    </form>
  )
}