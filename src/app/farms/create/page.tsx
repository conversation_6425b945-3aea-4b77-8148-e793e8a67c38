'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { ImageUpload } from '@/components/ui/FileUpload'
import { farmService } from '@/lib/firebase/firestore'
import { ActivityType } from '@/lib/types/firestore'
import { SOUTH_AFRICAN_PROVINCES, SouthAfricanProvince } from '@/lib/constants'

interface FarmFormData {
  name: string
  description: string
  descriptionAfrikaans: string
  location: string
  province: SouthAfricanProvince | ''
  sizeHectares: string
  activityTypes: ActivityType
  contactEmail: string
  contactPhone: string
  websiteUrl: string
  rules: string
  rulesAfrikaans: string
  pricingInfo: string
}



export default function CreateFarmPage() {
  const { user, loading: authLoading } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [uploadedImages, setUploadedImages] = useState<string[]>([])

  const [formData, setFormData] = useState<FarmFormData>({
    name: '',
    description: '',
    descriptionAfrikaans: '',
    location: '',
    province: '',
    sizeHectares: '',
    activityTypes: 'both',
    contactEmail: '',
    contactPhone: '',
    websiteUrl: '',
    rules: '',
    rulesAfrikaans: '',
    pricingInfo: ''
  })

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/login')
      return
    }

    // TODO: Check if user is farm owner using custom claims or user profile
    // For now, allow all authenticated users to create farms

    // Pre-fill contact email with user's email
    if (user?.email) {
      setFormData(prev => ({ ...prev, contactEmail: user.email! }))
    }
  }, [user, authLoading, router])

  const handleInputChange = (field: keyof FarmFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setError(null)
  }

  const handleImageUpload = (imageUrl: string) => {
    setUploadedImages(prev => [...prev, imageUrl])
  }

  const handleImageRemove = (imageUrl: string) => {
    setUploadedImages(prev => prev.filter(url => url !== imageUrl))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // Validate required fields
      if (!formData.name || !formData.location || !formData.province || !formData.contactEmail) {
        setError('Please fill in all required fields')
        setLoading(false)
        return
      }

      // Validate website URL if provided
      if (formData.websiteUrl && formData.websiteUrl.trim()) {
        const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
        if (!urlPattern.test(formData.websiteUrl.trim())) {
          setError('Please enter a valid website URL')
          setLoading(false)
          return
        }
      }

      const farmData = {
        ownerId: user!.uid,
        name: formData.name,
        description: formData.description || undefined,
        descriptionAfrikaans: formData.descriptionAfrikaans || undefined,
        location: formData.location,
        province: formData.province as SouthAfricanProvince,
        sizeHectares: formData.sizeHectares ? parseInt(formData.sizeHectares) : undefined,
        activityTypes: formData.activityTypes,
        contactEmail: formData.contactEmail,
        contactPhone: formData.contactPhone || undefined,
        websiteUrl: formData.websiteUrl || undefined,
        rules: formData.rules || undefined,
        rulesAfrikaans: formData.rulesAfrikaans || undefined,
        pricingInfo: formData.pricingInfo || undefined,
        isActive: true,
        featured: false
      }

      const farmId = await farmService.create(farmData)

      // Handle farm images - they're already uploaded to Firebase Storage
      // The uploadedImages array contains the Firebase Storage URLs
      if (uploadedImages.length > 0) {
        console.log(`Farm created with ${uploadedImages.length} images uploaded to Firebase Storage`)
        // Images are already stored in Firebase Storage and URLs are in uploadedImages
        // We could optionally store these URLs in a farm_images subcollection
        // For now, the images are uploaded and accessible via their URLs
      }

      router.push(`/farms/${farmId}`)
    } catch (err) {
      setError('An unexpected error occurred')
      console.error('Farm creation error:', err)
    } finally {
      setLoading(false)
    }
  }

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-earth-100">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600"></div>
      </div>
    )
  }

  // TODO: Implement proper role checking with Firebase custom claims
  // For now, allow all authenticated users to create farms
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-earth-100">
        <div className="text-center">
          <p className="text-earth-600 mb-4">Please log in to create a farm listing.</p>
          <Button onClick={() => router.push('/auth/login')}>
            Go to Login
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-earth-100 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-earth-900">Create Farm Listing</h1>
          <p className="text-earth-600 mt-2">
            Add your game farm to BvR Safaris and start receiving bookings
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-earth-700 mb-2">
                  Farm Name *
                </label>
                <Input
                  id="name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter your farm name"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-earth-700 mb-2">
                    Location/City *
                  </label>
                  <Input
                    id="location"
                    type="text"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder="e.g., Lephalale"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="province" className="block text-sm font-medium text-earth-700 mb-2">
                    Province *
                  </label>
                  <select
                    id="province"
                    value={formData.province}
                    onChange={(e) => handleInputChange('province', e.target.value)}
                    className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                    required
                  >
                    <option value="">Select Province</option>
                    {SOUTH_AFRICAN_PROVINCES.map(province => (
                      <option key={province} value={province}>{province}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="sizeHectares" className="block text-sm font-medium text-earth-700 mb-2">
                    Size (Hectares)
                  </label>
                  <Input
                    id="sizeHectares"
                    type="number"
                    value={formData.sizeHectares}
                    onChange={(e) => handleInputChange('sizeHectares', e.target.value)}
                    placeholder="e.g., 5000"
                  />
                </div>

                <div>
                  <label htmlFor="activityTypes" className="block text-sm font-medium text-earth-700 mb-2">
                    Activity Types *
                  </label>
                  <select
                    id="activityTypes"
                    value={formData.activityTypes}
                    onChange={(e) => handleInputChange('activityTypes', e.target.value as ActivityType)}
                    className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                    required
                  >
                    <option value="hunting">Hunting Only</option>
                    <option value="photo_safari">Photo Safari Only</option>
                    <option value="both">Both Hunting & Photo Safari</option>
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-earth-700 mb-2">
                  Description (English)
                </label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe your farm, facilities, and what makes it special..."
                  rows={4}
                  className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="descriptionAfrikaans" className="block text-sm font-medium text-earth-700 mb-2">
                  Description (Afrikaans)
                </label>
                <textarea
                  id="descriptionAfrikaans"
                  value={formData.descriptionAfrikaans}
                  onChange={(e) => handleInputChange('descriptionAfrikaans', e.target.value)}
                  placeholder="Beskryf jou plaas, fasiliteite, en wat dit spesiaal maak..."
                  rows={4}
                  className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                />
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="contactEmail" className="block text-sm font-medium text-earth-700 mb-2">
                    Contact Email *
                  </label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={formData.contactEmail}
                    onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="contactPhone" className="block text-sm font-medium text-earth-700 mb-2">
                    Contact Phone
                  </label>
                  <Input
                    id="contactPhone"
                    type="tel"
                    value={formData.contactPhone}
                    onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                    placeholder="+27 12 345 6789"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="websiteUrl" className="block text-sm font-medium text-earth-700 mb-2">
                  Website URL
                </label>
                <Input
                  id="websiteUrl"
                  type="text"
                  value={formData.websiteUrl}
                  onChange={(e) => handleInputChange('websiteUrl', e.target.value)}
                  placeholder="www.yourfarm.com"
                />
              </div>
            </CardContent>
          </Card>

          {/* Additional Information */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label htmlFor="pricingInfo" className="block text-sm font-medium text-earth-700 mb-2">
                  Pricing Information
                </label>
                <textarea
                  id="pricingInfo"
                  value={formData.pricingInfo}
                  onChange={(e) => handleInputChange('pricingInfo', e.target.value)}
                  placeholder="Provide general pricing information or mention 'Contact for pricing'..."
                  rows={3}
                  className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="rules" className="block text-sm font-medium text-earth-700 mb-2">
                  Rules & Regulations (English)
                </label>
                <textarea
                  id="rules"
                  value={formData.rules}
                  onChange={(e) => handleInputChange('rules', e.target.value)}
                  placeholder="List important rules, safety requirements, what to bring, etc..."
                  rows={4}
                  className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="rulesAfrikaans" className="block text-sm font-medium text-earth-700 mb-2">
                  Rules & Regulations (Afrikaans)
                </label>
                <textarea
                  id="rulesAfrikaans"
                  value={formData.rulesAfrikaans}
                  onChange={(e) => handleInputChange('rulesAfrikaans', e.target.value)}
                  placeholder="Lys belangrike reëls, veiligheidsvereistes, wat om te bring, ens..."
                  rows={4}
                  className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                />
              </div>
            </CardContent>
          </Card>

          {/* Farm Images */}
          <Card>
            <CardHeader>
              <CardTitle>Farm Images</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-earth-700 mb-4">
                  Upload Farm Photos
                </label>
                <p className="text-sm text-earth-600 mb-4">
                  Add photos of your farm, facilities, wildlife, and accommodations. The first image will be used as the main photo.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Uploaded Images */}
                  {uploadedImages.map((imageUrl, index) => (
                    <div key={imageUrl} className="relative group">
                      <div className="relative w-full h-48 rounded-lg border overflow-hidden">
                        <Image
                          src={imageUrl}
                          alt={`Farm image ${index + 1}`}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      </div>
                      <div className="absolute top-2 left-2">
                        {index === 0 && (
                          <span className="bg-accent-600 text-white text-xs px-2 py-1 rounded">
                            Main Photo
                          </span>
                        )}
                      </div>
                      <button
                        type="button"
                        onClick={() => handleImageRemove(imageUrl)}
                        className="absolute top-2 right-2 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        ×
                      </button>
                    </div>
                  ))}

                  {/* Upload New Image */}
                  {uploadedImages.length < 10 && user && (
                    <ImageUpload
                      bucket="farm-images"
                      farmId={user.uid}
                      onUpload={handleImageUpload}
                      maxSize={10}
                      className="h-48"
                    />
                  )}
                </div>

                {uploadedImages.length >= 10 && (
                  <p className="text-sm text-earth-500 mt-2">
                    Maximum of 10 images allowed. Remove an image to add more.
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Submit Buttons */}
          <div className="flex gap-4">
            <Button
              type="submit"
              variant="primary"
              isLoading={loading}
              className="flex-1"
            >
              {loading ? 'Creating Farm...' : 'Create Farm Listing'}
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className="flex-1"
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
