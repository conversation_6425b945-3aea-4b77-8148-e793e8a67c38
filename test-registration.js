const puppeteer = require('puppeteer');

async function testRegistrationFlow() {
  console.log('🚀 Starting registration flow test...');
  
  // Launch browser in non-headless mode so you can see what's happening
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: [
      '--start-maximized',
      '--no-sandbox',
      '--disable-setuid-sandbox'
    ]
  });

  const page = await browser.newPage();
  
  try {
    // Set a longer timeout for navigation
    page.setDefaultTimeout(30000);
    
    console.log('📱 Navigating to registration page...');
    
    // Navigate to the registration page
    // Assuming the dev server is running on localhost:3000
    await page.goto('http://localhost:3000/auth/register', { 
      waitUntil: 'networkidle2' 
    });
    
    console.log('✅ Registration page loaded');
    
    // Wait for the form to be visible
    await page.waitForSelector('form', { visible: true });
    
    console.log('📝 Filling out registration form...');
    
    // Generate test data with timestamp to ensure unique email
    const timestamp = Date.now();
    const testData = {
      firstName: 'John',
      lastName: 'Doe',
      email: `test.user.${timestamp}@example.com`,
      phone: '+1234567890',
      role: 'hunter',
      password: 'testpassword123'
    };
    
    console.log(`📧 Using test email: ${testData.email}`);
    
    // Fill out the form fields
    await page.type('#firstName', testData.firstName);
    console.log('✅ First name filled');
    
    await page.type('#lastName', testData.lastName);
    console.log('✅ Last name filled');
    
    await page.type('#email', testData.email);
    console.log('✅ Email filled');
    
    await page.type('#phone', testData.phone);
    console.log('✅ Phone filled');
    
    // Select role from dropdown
    await page.select('#role', testData.role);
    console.log('✅ Role selected');
    
    await page.type('#password', testData.password);
    console.log('✅ Password filled');
    
    await page.type('#confirmPassword', testData.password);
    console.log('✅ Confirm password filled');
    
    console.log('🎯 Form completed, ready to submit...');
    console.log('⏳ Waiting 3 seconds for you to review the form...');
    
    // Wait a bit so you can see the filled form
    await page.waitForTimeout(3000);
    
    // Take a screenshot before submission
    await page.screenshot({ 
      path: `registration-form-filled-${timestamp}.png`,
      fullPage: true 
    });
    console.log('📸 Screenshot taken: registration-form-filled-' + timestamp + '.png');
    
    // Submit the form
    console.log('🚀 Submitting registration form...');
    await page.click('button[type="submit"]');
    
    // Wait for either success redirect or error message
    console.log('⏳ Waiting for response...');
    
    try {
      // Wait for either dashboard redirect or error message
      await Promise.race([
        // Success case - redirect to dashboard
        page.waitForNavigation({ 
          url: 'http://localhost:3000/dashboard',
          timeout: 10000 
        }).then(() => 'success'),
        
        // Error case - error message appears
        page.waitForSelector('.bg-red-50', { 
          visible: true, 
          timeout: 10000 
        }).then(() => 'error')
      ]);
      
      const currentUrl = page.url();
      console.log(`📍 Current URL: ${currentUrl}`);
      
      if (currentUrl.includes('/dashboard')) {
        console.log('🎉 SUCCESS: Registration completed! Redirected to dashboard.');
        
        // Take a screenshot of the dashboard
        await page.screenshot({ 
          path: `registration-success-dashboard-${timestamp}.png`,
          fullPage: true 
        });
        console.log('📸 Dashboard screenshot taken');
        
      } else {
        // Check for error messages
        const errorElements = await page.$$('.bg-red-50 .text-red-600');
        if (errorElements.length > 0) {
          const errorText = await page.evaluate(el => el.textContent, errorElements[0]);
          console.log('❌ REGISTRATION ERROR:', errorText);
          
          // Take a screenshot of the error
          await page.screenshot({ 
            path: `registration-error-${timestamp}.png`,
            fullPage: true 
          });
          console.log('📸 Error screenshot taken');
        }
      }
      
    } catch (timeoutError) {
      console.log('⏰ Timeout waiting for response. Taking screenshot...');
      await page.screenshot({ 
        path: `registration-timeout-${timestamp}.png`,
        fullPage: true 
      });
      console.log('📸 Timeout screenshot taken');
    }
    
    console.log('⏳ Keeping browser open for 10 seconds for manual inspection...');
    await page.waitForTimeout(10000);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Take a screenshot of the error state
    const timestamp = Date.now();
    await page.screenshot({ 
      path: `registration-test-error-${timestamp}.png`,
      fullPage: true 
    });
    console.log('📸 Error screenshot taken');
    
  } finally {
    console.log('🔚 Closing browser...');
    await browser.close();
  }
}

// Check if this script is being run directly
if (require.main === module) {
  testRegistrationFlow().catch(console.error);
}

module.exports = { testRegistrationFlow };
